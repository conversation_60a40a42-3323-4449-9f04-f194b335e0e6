import React from 'react'
import { useAppStore } from '../../../store'
import { useArtifactToasts } from './ArtifactToast'

export function ArtifactToolbar() {
  const {
    artifacts: { isFullscreen, currentArtifact },
    closeArtifacts,
    toggleArtifactsFullscreen
  } = useAppStore()

  const { success, error } = useArtifactToasts()

  const handleCopyContent = async () => {
    if (!currentArtifact) return

    try {
      await navigator.clipboard.writeText(currentArtifact.content)
      success('Content copied to clipboard')
    } catch (err) {
      console.error('Failed to copy content:', err)
      error('Failed to copy content')
    }
  }

  const handleDownload = () => {
    if (!currentArtifact) return

    try {
      const blob = new Blob([currentArtifact.content], {
        type: getContentType(currentArtifact.type)
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = getFileName(currentArtifact)
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      success('File downloaded successfully')
    } catch (err) {
      console.error('Failed to download file:', err)
      error('Failed to download file')
    }
  }

  return (
    <div className="p-4 border-b border-tertiary">
      <div className="flex items-center bg-neutral-800/50 border-b border-neutral-700 relative">
        {/* All icons in flex gap-1 */}
        <div className="flex items-center gap-1">
          {/* Collapsed state: Essential actions only */}
          {!isFullscreen && (
            <>
              {/* Fullscreen toggle - always visible in collapsed */}
              <button
                onClick={toggleArtifactsFullscreen}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title="Enter fullscreen"
              >
                <i className="fa-solid fa-expand text-gray-400"></i>
              </button>

              {/* Close button - always visible */}
              <button
                onClick={closeArtifacts}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title="Close artifacts"
              >
                <i className="fa-solid fa-xmark text-gray-400"></i>
              </button>
            </>
          )}

          {/* Fullscreen state: All actions available */}
          {isFullscreen && (
            <>
              {/* Copy button */}
              {currentArtifact && (
                <button
                  onClick={handleCopyContent}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                  title="Copy content"
                >
                  <i className="fa-solid fa-copy text-gray-400"></i>
                </button>
              )}

              {/* Download button */}
              {currentArtifact && (
                <button
                  onClick={handleDownload}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                  title="Download file"
                >
                  <i className="fa-solid fa-download text-gray-400"></i>
                </button>
              )}

              {/* Exit fullscreen */}
              <button
                onClick={toggleArtifactsFullscreen}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title="Exit fullscreen"
              >
                <i className="fa-solid fa-compress text-gray-400"></i>
              </button>

              {/* Close button */}
              <button
                onClick={closeArtifacts}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title="Close artifacts"
              >
                <i className="fa-solid fa-xmark text-gray-400"></i>
              </button>
            </>
          )}
        </div>

        {/* Hide before full screen: flex-1 overflow-hidden */}
        <div className={`flex-1 overflow-hidden ${!isFullscreen ? 'hidden' : ''}`}>
          <h2 className="text-lg font-semibold text-supplement1 ml-4">Artifacts</h2>
        </div>
      </div>
    </div>
  )
}

// Helper functions
function getContentType(artifactType: string): string {
  switch (artifactType) {
    case 'code':
      return 'text/plain'
    case 'json':
      return 'application/json'
    case 'html':
      return 'text/html'
    case 'markdown':
      return 'text/markdown'
    case 'mermaid':
      return 'text/plain'
    case 'image':
      return 'image/png'
    default:
      return 'text/plain'
  }
}

function getFileName(artifact: any): string {
  const extension = getFileExtension(artifact.type, artifact.metadata?.language)
  const baseName = artifact.title.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()
  return `${baseName}.${extension}`
}

function getFileExtension(type: string, language?: string): string {
  switch (type) {
    case 'code':
      return getCodeExtension(language)
    case 'json':
      return 'json'
    case 'html':
      return 'html'
    case 'markdown':
      return 'md'
    case 'mermaid':
      return 'mmd'
    case 'image':
      return 'png'
    default:
      return 'txt'
  }
}

function getCodeExtension(language?: string): string {
  const extensions: Record<string, string> = {
    javascript: 'js',
    typescript: 'ts',
    python: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    csharp: 'cs',
    php: 'php',
    ruby: 'rb',
    go: 'go',
    rust: 'rs',
    swift: 'swift',
    kotlin: 'kt',
    scala: 'scala',
    sql: 'sql',
    bash: 'sh',
    powershell: 'ps1',
    yaml: 'yml',
    xml: 'xml',
    css: 'css',
    scss: 'scss',
    less: 'less'
  }
  
  return extensions[language?.toLowerCase() || ''] || 'txt'
}
